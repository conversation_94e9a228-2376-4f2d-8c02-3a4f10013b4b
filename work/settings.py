import os
from pathlib import Path
from decouple import Config, RepositoryEnv, Csv

BASE_DIR = Path(__file__).resolve().parent.parent
config = Config(RepositoryEnv(os.path.join(BASE_DIR, '.env')))

config = Config(RepositoryEnv('.env'))

MODE=config('MODE')
SECRET_KEY = config('SECRET_KEY'')
DEBUG = config('DEBUG', default=False, cast=bool)
ALLOWED_HOSTS = config('ALLOWED_HOSTS', cast=Csv())
FORCE_SCRIPT_NAME = '/work'
STATIC_URL = '/work/static/'
MEDIA_URL = '/work/media/'

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': config('DB_NAME'),
        'USER': config('DB_USER'),
        'PASSWORD': config('DB_PASSWORD'),
        'HOST': config('DB_HOST'),
        'PORT': config('DB_PORT'),
    }
}
